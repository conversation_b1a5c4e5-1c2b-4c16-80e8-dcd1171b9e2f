#!/bin/bash

# tcpping 参数测试脚本
# 用于测试不同参数组合的执行时间

HOST=${1:-"127.0.0.1"}
PORT=${2:-"80"}

echo "=== tcpping 参数测试 ==="
echo "目标: $HOST:$PORT"
echo

# 检查 tcpping 是否存在
if ! command -v tcpping >/dev/null 2>&1; then
    echo "❌ tcpping 命令不存在，请先安装"
    exit 1
fi

# 测试函数
test_tcping() {
    local desc="$1"
    local cmd="$2"
    
    echo "测试: $desc"
    echo "命令: $cmd"
    
    # 记录开始时间
    start_time=$(date +%s.%N)
    
    # 执行命令
    result=$(eval $cmd 2>/dev/null)
    exit_code=$?
    
    # 记录结束时间
    end_time=$(date +%s.%N)
    
    # 计算执行时间
    execution_time=$(echo "$end_time - $start_time" | bc 2>/dev/null || echo "计算失败")
    
    echo "执行时间: ${execution_time}s"
    echo "退出码: $exit_code"
    echo "结果: $result"
    echo "---"
}

echo "1. 默认参数测试（可能很慢）:"
test_tcping "默认参数 -c 3" "tcpping $HOST -p $PORT -c 3"

echo "2. 优化间隔测试:"
test_tcping "快速间隔 -c 3 -i 0.2" "tcpping $HOST -p $PORT -c 3 -i 0.2"

echo "3. 更激进的优化:"
test_tcping "极速模式 -c 3 -i 0.1 -t 1" "tcpping $HOST -p $PORT -c 3 -i 0.1 -t 1"

echo "4. 单次测试（最快）:"
test_tcping "单次测试 -c 1" "tcpping $HOST -p $PORT -c 1"

echo "5. 双次测试（平衡）:"
test_tcping "双次测试 -c 2 -i 0.2" "tcpping $HOST -p $PORT -c 2 -i 0.2"

echo
echo "=== 建议 ==="
echo "1. 如果执行时间 > 3秒，建议减少测试次数或间隔"
echo "2. 对于 Zabbix 监控，建议执行时间 < 2秒"
echo "3. 可以根据网络环境调整 -t 超时参数"
