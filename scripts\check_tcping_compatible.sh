#!/bin/bash

# Zabbix TCP ping 监控脚本 (兼容版本)
# 用法: check_tcping_compatible.sh <HOST> <PORT>
# 返回: 连接延迟(ms) 或 -1(失败)
# 特点: 最大兼容性，逐步尝试不同参数

HOST=$1
PORT=$2

# 参数检查
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    echo -1
    exit 1
fi

# 检查 tcpping 命令是否存在
if ! command -v tcpping >/dev/null 2>&1; then
    echo -1
    exit 1
fi

# 尝试不同的 tcpping 参数组合
try_tcpping() {
    local cmd="$1"
    local desc="$2"
    
    # 执行命令并捕获输出
    local output=$(eval "$cmd" 2>/dev/null)
    local exit_code=$?
    
    # 如果命令成功执行
    if [ $exit_code -eq 0 ] && [ -n "$output" ]; then
        # 尝试解析时间
        local result=$(echo "$output" | grep 'time=' | head -1 | awk -F'time=' '{print $2}' | awk '{gsub(/[^0-9.]/, "", $1); if($1 > 0) printf "%.3f", $1}')
        
        if [ -n "$result" ] && [[ "$result" =~ ^[0-9]+\.?[0-9]*$ ]]; then
            echo "$result"
            return 0
        fi
    fi
    
    return 1
}

# 按优先级尝试不同的参数组合

# 1. 尝试最简单的参数
if try_tcpping "timeout 3 tcpping $HOST -p $PORT -c 1" "简单参数"; then
    exit 0
fi

# 2. 尝试带超时参数
if try_tcpping "timeout 3 tcpping $HOST -p $PORT -c 1 -t 2" "带超时参数"; then
    exit 0
fi

# 3. 尝试您原来的参数格式（可能使用 -x 而不是 -c）
if try_tcpping "timeout 3 tcpping $HOST -p $PORT -x 1" "使用 -x 参数"; then
    exit 0
fi

# 4. 尝试不同的命令格式
if try_tcpping "timeout 3 tcpping $HOST $PORT" "无参数格式"; then
    exit 0
fi

# 5. 最后尝试：检查端口是否开放（使用 nc 作为备选）
if command -v nc >/dev/null 2>&1; then
    start_time=$(date +%s%3N)
    if timeout 2 nc -z $HOST $PORT >/dev/null 2>&1; then
        end_time=$(date +%s%3N)
        result=$((end_time - start_time))
        if [ $result -gt 0 ]; then
            echo "$result.000"
            exit 0
        fi
    fi
fi

# 所有方法都失败
echo -1
exit 1
