#!/bin/bash

# Zabbix TCP ping 监控脚本 (增强版)
# 用法: check_tcping_enhanced.sh <HOST> <PORT> [COUNT] [TIMEOUT]
# 返回: 平均延迟(ms) 或 -1(失败)

HOST=$1
PORT=$2
COUNT=${3:-3}
TIMEOUT=${4:-5}

# 日志函数
log_debug() {
    if [ "$DEBUG" = "1" ]; then
        echo "[DEBUG] $1" >&2
    fi
}

# 参数验证
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    log_debug "缺少必要参数: HOST=$HOST, PORT=$PORT"
    echo -1
    exit 1
fi

# 验证端口号
if ! [[ "$PORT" =~ ^[1-9][0-9]*$ ]] || [ "$PORT" -gt 65535 ]; then
    log_debug "无效端口号: $PORT"
    echo -1
    exit 1
fi

# 验证测试次数
if ! [[ "$COUNT" =~ ^[1-9][0-9]*$ ]] || [ "$COUNT" -gt 100 ]; then
    log_debug "无效测试次数，使用默认值: $COUNT -> 3"
    COUNT=3
fi

# 验证超时时间
if ! [[ "$TIMEOUT" =~ ^[1-9][0-9]*$ ]] || [ "$TIMEOUT" -gt 60 ]; then
    log_debug "无效超时时间，使用默认值: $TIMEOUT -> 5"
    TIMEOUT=5
fi

log_debug "开始测试: $HOST:$PORT, 次数=$COUNT, 超时=$TIMEOUT"

# 检查 tcpping 命令是否存在
if ! command -v tcpping >/dev/null 2>&1; then
    log_debug "tcpping 命令未找到"
    echo -1
    exit 1
fi

# 执行 tcpping 命令
# 使用 timeout 命令防止长时间挂起
TCPPING_OUTPUT=$(timeout $((TIMEOUT * COUNT + 5)) tcpping $HOST -p $PORT -c $COUNT 2>/dev/null)
TCPPING_EXIT_CODE=$?

log_debug "tcpping 退出码: $TCPPING_EXIT_CODE"
log_debug "tcpping 输出: $TCPPING_OUTPUT"

# 检查命令执行是否成功
if [ $TCPPING_EXIT_CODE -ne 0 ]; then
    log_debug "tcpping 命令执行失败"
    echo -1
    exit 1
fi

# 解析结果并计算平均延迟
RESULT=$(echo "$TCPPING_OUTPUT" | \
    grep 'time=' | \
    awk -F'time=' '{
        # 提取时间值，去除单位和其他字符
        gsub(/[^0-9.]/, "", $2)
        if ($2 > 0) {
            sum += $2
            count++
        }
    } END {
        if (count > 0) {
            printf "%.3f", sum/count
        } else {
            print -1
        }
    }')

log_debug "解析结果: $RESULT"

# 验证结果
if [ -z "$RESULT" ] || [ "$RESULT" = "-1" ]; then
    echo -1
else
    # 确保结果是有效的数字
    if [[ "$RESULT" =~ ^[0-9]+\.?[0-9]*$ ]]; then
        echo $RESULT
    else
        log_debug "无效的结果格式: $RESULT"
        echo -1
    fi
fi
