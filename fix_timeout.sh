#!/bin/bash

# 修复 Zabbix Agent 超时问题

echo "修复 Zabbix Agent 超时配置..."

# 备份原配置文件
if [ -f "/etc/zabbix/zabbix_agentd.conf" ]; then
    sudo cp /etc/zabbix/zabbix_agentd.conf /etc/zabbix/zabbix_agentd.conf.backup.$(date +%Y%m%d_%H%M%S)
    echo "已备份原配置文件"
fi

# 检查并修改超时配置
ZABBIX_CONF="/etc/zabbix/zabbix_agentd.conf"

if [ -f "$ZABBIX_CONF" ]; then
    # 检查当前 Timeout 设置
    CURRENT_TIMEOUT=$(grep "^Timeout=" $ZABBIX_CONF | cut -d'=' -f2)
    
    if [ -z "$CURRENT_TIMEOUT" ]; then
        echo "添加 Timeout 配置..."
        echo "Timeout=10" | sudo tee -a $ZABBIX_CONF
    else
        echo "当前 Timeout: $CURRENT_TIMEOUT"
        if [ "$CURRENT_TIMEOUT" -lt 10 ]; then
            echo "增加 Timeout 到 10 秒..."
            sudo sed -i "s/^Timeout=.*/Timeout=10/" $ZABBIX_CONF
        fi
    fi
    
    # 重启 Zabbix Agent
    echo "重启 Zabbix Agent..."
    if systemctl is-active --quiet zabbix-agent; then
        sudo systemctl restart zabbix-agent
        echo "zabbix-agent 已重启"
    elif systemctl is-active --quiet zabbix-agentd; then
        sudo systemctl restart zabbix-agentd
        echo "zabbix-agentd 已重启"
    else
        echo "请手动重启 Zabbix Agent"
    fi
    
    echo "配置修复完成"
else
    echo "错误：找不到 Zabbix Agent 配置文件"
fi
