#!/bin/bash

# Zabbix TCP ping 问题排查脚本
# 用法: ./debug_tcping.sh

echo "=== Zabbix TCP ping 问题排查 ==="
echo

# 1. 检查脚本文件
echo "1. 检查脚本文件："
if [ -f "/etc/zabbix/scripts/check_tcping.sh" ]; then
    echo "✅ 脚本文件存在: /etc/zabbix/scripts/check_tcping.sh"
    ls -la /etc/zabbix/scripts/check_tcping.sh
else
    echo "❌ 脚本文件不存在: /etc/zabbix/scripts/check_tcping.sh"
fi
echo

# 2. 检查 tcpping 命令
echo "2. 检查 tcpping 命令："
if command -v tcpping >/dev/null 2>&1; then
    echo "✅ tcpping 命令存在: $(which tcpping)"
else
    echo "❌ tcpping 命令不存在"
fi
echo

# 3. 检查 zabbix 用户权限
echo "3. 检查 zabbix 用户权限："
if id zabbix >/dev/null 2>&1; then
    echo "✅ zabbix 用户存在"
    echo "zabbix 用户信息: $(id zabbix)"
    
    # 测试 zabbix 用户执行脚本
    echo "测试 zabbix 用户执行脚本："
    if [ -f "/etc/zabbix/scripts/check_tcping.sh" ]; then
        echo "执行: sudo -u zabbix /etc/zabbix/scripts/check_tcping.sh 127.0.0.1 80 3"
        timeout 10 sudo -u zabbix /etc/zabbix/scripts/check_tcping.sh 127.0.0.1 80 3
        echo "退出码: $?"
    fi
else
    echo "❌ zabbix 用户不存在"
fi
echo

# 4. 检查网络连通性
echo "4. 检查网络连通性："
echo "测试 127.0.0.1:80 连通性："
if command -v nc >/dev/null 2>&1; then
    timeout 5 nc -zv 127.0.0.1 80 2>&1
elif command -v telnet >/dev/null 2>&1; then
    timeout 5 telnet 127.0.0.1 80 2>&1
else
    echo "⚠️ 无法测试连通性（nc 和 telnet 都不可用）"
fi
echo

# 5. 检查 Zabbix Agent 配置
echo "5. 检查 Zabbix Agent 配置："
if [ -f "/etc/zabbix/zabbix_agentd.d/tcping.conf" ]; then
    echo "✅ 配置文件存在: /etc/zabbix/zabbix_agentd.d/tcping.conf"
    echo "配置内容："
    grep -v "^#" /etc/zabbix/zabbix_agentd.d/tcping.conf | grep -v "^$"
else
    echo "❌ 配置文件不存在: /etc/zabbix/zabbix_agentd.d/tcping.conf"
fi
echo

# 6. 测试 zabbix_agentd
echo "6. 测试 zabbix_agentd："
if command -v zabbix_agentd >/dev/null 2>&1; then
    echo "测试配置项："
    echo "zabbix_agentd -t tcping.port[127.0.0.1,80]"
    timeout 15 zabbix_agentd -t tcping.port[127.0.0.1,80] 2>&1
    echo "退出码: $?"
else
    echo "❌ zabbix_agentd 命令不存在"
fi
echo

# 7. 检查系统资源
echo "7. 检查系统资源："
echo "内存使用情况："
free -h
echo
echo "CPU 负载："
uptime
echo
echo "磁盘空间："
df -h /tmp /var/log
echo

echo "=== 排查完成 ==="
echo
echo "常见解决方案："
echo "1. 如果脚本不存在，运行: sudo ./deploy.sh"
echo "2. 如果权限问题，运行: sudo chown zabbix:zabbix /etc/zabbix/scripts/check_tcping.sh"
echo "3. 如果超时问题，增加 Zabbix Agent 的 Timeout 配置"
echo "4. 如果 tcpping 不存在，安装: sudo yum install tcptraceroute"
