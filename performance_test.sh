#!/bin/bash

# Zabbix 监控脚本性能测试工具
# 用法: ./performance_test.sh [测试类型] [并发数] [目标主机] [端口]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认参数
TEST_TYPE=${1:-"both"}      # both, tcpping, nmap
CONCURRENT=${2:-10}         # 并发数
TARGET_HOST=${3:-"*******"} # 目标主机
TARGET_PORT=${4:-53}        # 目标端口

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if [ "$TEST_TYPE" = "both" ] || [ "$TEST_TYPE" = "tcpping" ]; then
        if ! command -v tcpping >/dev/null 2>&1; then
            log_error "tcpping 命令未找到"
            exit 1
        fi
    fi
    
    if [ "$TEST_TYPE" = "both" ] || [ "$TEST_TYPE" = "nmap" ]; then
        if ! command -v nmap >/dev/null 2>&1; then
            log_error "nmap 命令未找到"
            exit 1
        fi
    fi
    
    log_success "依赖检查通过"
}

# 获取系统资源使用情况
get_system_stats() {
    local prefix=$1
    
    # CPU 使用率
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    
    # 内存使用情况
    local mem_info=$(free -m | grep "Mem:")
    local mem_total=$(echo $mem_info | awk '{print $2}')
    local mem_used=$(echo $mem_info | awk '{print $3}')
    local mem_usage=$((mem_used * 100 / mem_total))
    
    # 负载平均值
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo "${prefix}_cpu_usage:${cpu_usage}%"
    echo "${prefix}_mem_usage:${mem_usage}%"
    echo "${prefix}_load_avg:${load_avg}"
}

# 单次性能测试
single_test() {
    local method=$1
    local host=$2
    local port=$3
    
    log_info "执行单次 $method 测试..."
    
    if [ "$method" = "tcpping" ]; then
        local cmd="tcpping $host -p $port -c 3 2>/dev/null | grep 'time=' | awk -F'time=' '{print \$2}' | awk '{gsub(/[^0-9.]/, \"\", \$1); sum+=\$1; count++} END {if(count>0) printf \"%.3f\", sum/count; else print -1}'"
    else
        local cmd="nmap -Pn $host -p$port 2>/dev/null | grep -B3 'tcp open' | grep 'Host' | awk -F '[(s]' '{print \$4*1000}'"
    fi
    
    # 记录开始时间和系统状态
    local start_time=$(date +%s.%N)
    get_system_stats "before" > /tmp/${method}_before.stats
    
    # 执行命令
    local result=$(eval $cmd)
    
    # 记录结束时间和系统状态
    local end_time=$(date +%s.%N)
    get_system_stats "after" > /tmp/${method}_after.stats
    
    # 计算执行时间
    local execution_time=$(echo "$end_time - $start_time" | bc)
    
    echo "方法: $method"
    echo "执行时间: ${execution_time}s"
    echo "结果: $result"
    echo "---"
}

# 并发性能测试
concurrent_test() {
    local method=$1
    local concurrent_num=$2
    local host=$3
    local port=$4
    
    log_info "执行 $method 并发测试 (并发数: $concurrent_num)..."
    
    # 准备命令
    if [ "$method" = "tcpping" ]; then
        local cmd="tcpping $host -p $port -c 3 >/dev/null 2>&1"
    else
        local cmd="nmap -Pn $host -p$port >/dev/null 2>&1"
    fi
    
    # 记录开始状态
    local start_time=$(date +%s.%N)
    get_system_stats "before" > /tmp/${method}_concurrent_before.stats
    
    # 启动并发进程
    local pids=()
    for i in $(seq 1 $concurrent_num); do
        eval $cmd &
        pids+=($!)
    done
    
    # 等待所有进程完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    # 记录结束状态
    local end_time=$(date +%s.%N)
    get_system_stats "after" > /tmp/${method}_concurrent_after.stats
    
    # 计算总执行时间
    local total_time=$(echo "$end_time - $start_time" | bc)
    
    echo "方法: $method"
    echo "并发数: $concurrent_num"
    echo "总执行时间: ${total_time}s"
    echo "平均每个任务: $(echo "scale=3; $total_time / $concurrent_num" | bc)s"
    
    # 显示系统资源变化
    echo "系统资源变化:"
    echo "执行前: $(cat /tmp/${method}_concurrent_before.stats | tr '\n' ' ')"
    echo "执行后: $(cat /tmp/${method}_concurrent_after.stats | tr '\n' ' ')"
    echo "---"
}

# 内存使用测试
memory_test() {
    local method=$1
    local host=$2
    local port=$3
    
    log_info "执行 $method 内存使用测试..."
    
    if [ "$method" = "tcpping" ]; then
        local cmd="tcpping $host -p $port -c 3"
    else
        local cmd="nmap -Pn $host -p$port"
    fi
    
    # 使用 time 命令获取详细资源使用情况
    /usr/bin/time -v $cmd >/dev/null 2>/tmp/${method}_memory.log
    
    # 提取关键信息
    local max_memory=$(grep "Maximum resident set size" /tmp/${method}_memory.log | awk '{print $6}')
    local user_time=$(grep "User time" /tmp/${method}_memory.log | awk '{print $4}')
    local system_time=$(grep "System time" /tmp/${method}_memory.log | awk '{print $4}')
    local cpu_percent=$(grep "Percent of CPU" /tmp/${method}_memory.log | awk '{print $7}')
    
    echo "方法: $method"
    echo "最大内存使用: ${max_memory} KB"
    echo "用户时间: ${user_time}s"
    echo "系统时间: ${system_time}s"
    echo "CPU使用率: $cpu_percent"
    echo "---"
}

# 生成测试报告
generate_report() {
    local report_file="performance_report_$(date +%Y%m%d_%H%M%S).txt"
    
    log_info "生成测试报告: $report_file"
    
    cat > $report_file << EOF
# Zabbix 监控脚本性能测试报告

## 测试环境
- 测试时间: $(date)
- 系统信息: $(uname -a)
- 目标主机: $TARGET_HOST:$TARGET_PORT
- 并发数: $CONCURRENT

## 测试结果

### 单次执行测试
$(cat /tmp/single_test_results.txt 2>/dev/null || echo "未执行单次测试")

### 并发执行测试
$(cat /tmp/concurrent_test_results.txt 2>/dev/null || echo "未执行并发测试")

### 内存使用测试
$(cat /tmp/memory_test_results.txt 2>/dev/null || echo "未执行内存测试")

## 建议

基于测试结果：
1. 如果 tcpping 在所有测试中表现更好，建议切换到 tcpping 方法
2. 如果系统资源紧张，考虑减少监控频率或增加缓存
3. 在大规模部署时，建议使用资源消耗更少的方法

EOF

    log_success "测试报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始性能测试..."
    log_info "测试类型: $TEST_TYPE, 并发数: $CONCURRENT, 目标: $TARGET_HOST:$TARGET_PORT"
    
    check_dependencies
    
    # 单次测试
    if [ "$TEST_TYPE" = "both" ] || [ "$TEST_TYPE" = "tcpping" ]; then
        single_test "tcpping" $TARGET_HOST $TARGET_PORT >> /tmp/single_test_results.txt
        memory_test "tcpping" $TARGET_HOST $TARGET_PORT >> /tmp/memory_test_results.txt
        concurrent_test "tcpping" $CONCURRENT $TARGET_HOST $TARGET_PORT >> /tmp/concurrent_test_results.txt
    fi
    
    if [ "$TEST_TYPE" = "both" ] || [ "$TEST_TYPE" = "nmap" ]; then
        single_test "nmap" $TARGET_HOST $TARGET_PORT >> /tmp/single_test_results.txt
        memory_test "nmap" $TARGET_HOST $TARGET_PORT >> /tmp/memory_test_results.txt
        concurrent_test "nmap" $CONCURRENT $TARGET_HOST $TARGET_PORT >> /tmp/concurrent_test_results.txt
    fi
    
    # 显示结果
    echo
    log_success "=== 测试结果 ==="
    echo
    
    if [ -f /tmp/single_test_results.txt ]; then
        echo "单次执行测试:"
        cat /tmp/single_test_results.txt
        echo
    fi
    
    if [ -f /tmp/memory_test_results.txt ]; then
        echo "内存使用测试:"
        cat /tmp/memory_test_results.txt
        echo
    fi
    
    if [ -f /tmp/concurrent_test_results.txt ]; then
        echo "并发执行测试:"
        cat /tmp/concurrent_test_results.txt
        echo
    fi
    
    generate_report
    
    # 清理临时文件
    rm -f /tmp/*_test_results.txt /tmp/*_*.stats /tmp/*_memory.log
    
    log_success "性能测试完成！"
}

# 显示帮助信息
show_help() {
    echo "Zabbix 监控脚本性能测试工具"
    echo
    echo "用法: $0 [测试类型] [并发数] [目标主机] [端口]"
    echo
    echo "参数:"
    echo "  测试类型    both|tcpping|nmap (默认: both)"
    echo "  并发数      并发执行的进程数 (默认: 10)"
    echo "  目标主机    测试目标主机 (默认: *******)"
    echo "  端口        测试目标端口 (默认: 53)"
    echo
    echo "示例:"
    echo "  $0                           # 使用默认参数测试两种方法"
    echo "  $0 tcpping 20 192.168.1.1 80 # 测试 tcpping，20并发，目标 192.168.1.1:80"
    echo "  $0 nmap 5 www.google.com 443  # 测试 nmap，5并发，目标 www.google.com:443"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main
        ;;
esac
