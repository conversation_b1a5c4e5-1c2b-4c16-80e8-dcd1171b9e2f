# Zabbix TCP ping 监控配置
# 将此配置添加到 /etc/zabbix/zabbix_agentd.d/tcping.conf

# 基础 TCP ping 检查（优化版，包间隔0.2秒）
UserParameter=check.tcping[*],/etc/zabbix/scripts/check_tcping.sh $1 $2 $3
UserParameter=tcping.port[*],/etc/zabbix/scripts/check_tcping.sh $1 $2 $3

# 快速版本（包间隔0.1秒，默认2次测试）
UserParameter=check.tcping.fast[*],/etc/zabbix/scripts/check_tcping_fast.sh $1 $2 $3

# 单次测试版本（最快，适合大规模监控）
UserParameter=check.tcping.single[*],/etc/zabbix/scripts/check_tcping_single.sh $1 $2

# 增强版 TCP ping 检查（包含超时控制）
UserParameter=check.tcping.enhanced[*],/etc/zabbix/scripts/check_tcping_enhanced.sh $1 $2 $3 $4

# 预定义的常用服务检查（带默认测试次数）
# 用法: check.tcping.http[主机名] 或 check.tcping.http[主机名,测试次数]
# 示例: check.tcping.http[www.google.com] 或 check.tcping.http[www.google.com,5]

UserParameter=check.tcping.http[*],/etc/zabbix/scripts/check_tcping.sh $1 80 ${2:-3}
UserParameter=check.tcping.https[*],/etc/zabbix/scripts/check_tcping.sh $1 443 ${2:-3}
UserParameter=check.tcping.ssh[*],/etc/zabbix/scripts/check_tcping.sh $1 22 ${2:-3}
UserParameter=check.tcping.ftp[*],/etc/zabbix/scripts/check_tcping.sh $1 21 ${2:-3}
UserParameter=check.tcping.smtp[*],/etc/zabbix/scripts/check_tcping.sh $1 25 ${2:-3}
UserParameter=check.tcping.dns[*],/etc/zabbix/scripts/check_tcping.sh $1 53 ${2:-3}
UserParameter=check.tcping.mysql[*],/etc/zabbix/scripts/check_tcping.sh $1 3306 ${2:-3}
UserParameter=check.tcping.redis[*],/etc/zabbix/scripts/check_tcping.sh $1 6379 ${2:-3}

# 固定参数版本（如果您更喜欢简单配置）：
# UserParameter=check.tcping.http.simple[*],/etc/zabbix/scripts/check_tcping.sh $1 80 3
# UserParameter=check.tcping.https.simple[*],/etc/zabbix/scripts/check_tcping.sh $1 443 3

# 快速检查版本（1次测试，适合大量监控项）：
# UserParameter=check.tcping.http.fast[*],/etc/zabbix/scripts/check_tcping.sh $1 80 1
# UserParameter=check.tcping.https.fast[*],/etc/zabbix/scripts/check_tcping.sh $1 443 1

# 高精度版本（10次测试，适合关键服务）：
# UserParameter=check.tcping.http.precise[*],/etc/zabbix/scripts/check_tcping.sh $1 80 10
# UserParameter=check.tcping.https.precise[*],/etc/zabbix/scripts/check_tcping.sh $1 443 10
