#!/bin/bash

# 调试单次 tcping 脚本的问题

echo "=== 调试 check_tcping_single.sh 脚本 ==="
echo

HOST=${1:-"127.0.0.1"}
PORT=${2:-"80"}

echo "测试目标: $HOST:$PORT"
echo

# 1. 检查脚本是否存在
echo "1. 检查脚本文件："
if [ -f "/etc/zabbix/scripts/check_tcping_single.sh" ]; then
    echo "✅ 脚本存在"
    ls -la /etc/zabbix/scripts/check_tcping_single.sh
else
    echo "❌ 脚本不存在"
    echo "请运行: sudo cp scripts/check_tcping_single.sh /etc/zabbix/scripts/"
    echo "然后设置权限: sudo chmod +x /etc/zabbix/scripts/check_tcping_single.sh"
    echo "设置所有者: sudo chown zabbix:zabbix /etc/zabbix/scripts/check_tcping_single.sh"
fi
echo

# 2. 检查 tcpping 命令
echo "2. 检查 tcpping 命令："
if command -v tcpping >/dev/null 2>&1; then
    echo "✅ tcpping 存在: $(which tcpping)"
    echo "版本信息:"
    tcpping --help 2>&1 | head -5
else
    echo "❌ tcpping 不存在"
    echo "安装命令: sudo yum install tcptraceroute"
fi
echo

# 3. 测试 tcpping 原始命令
echo "3. 测试 tcpping 原始命令："
echo "执行: tcpping $HOST -p $PORT -c 1 -t 2"
if command -v tcpping >/dev/null 2>&1; then
    tcpping $HOST -p $PORT -c 1 -t 2 2>&1
    echo "退出码: $?"
else
    echo "跳过（tcpping 不存在）"
fi
echo

# 4. 测试端口连通性
echo "4. 测试端口连通性："
if command -v nc >/dev/null 2>&1; then
    echo "使用 nc 测试:"
    timeout 3 nc -zv $HOST $PORT 2>&1
elif command -v telnet >/dev/null 2>&1; then
    echo "使用 telnet 测试:"
    timeout 3 telnet $HOST $PORT 2>&1
else
    echo "⚠️ 无法测试连通性（nc 和 telnet 都不可用）"
fi
echo

# 5. 手动执行脚本（如果存在）
echo "5. 手动执行脚本："
if [ -f "/etc/zabbix/scripts/check_tcping_single.sh" ]; then
    echo "直接执行:"
    /etc/zabbix/scripts/check_tcping_single.sh $HOST $PORT
    echo "退出码: $?"
    echo
    
    echo "使用 zabbix 用户执行:"
    sudo -u zabbix /etc/zabbix/scripts/check_tcping_single.sh $HOST $PORT
    echo "退出码: $?"
else
    echo "脚本不存在，跳过测试"
fi
echo

# 6. 测试简化版本的 tcpping
echo "6. 测试不同的 tcpping 参数："
if command -v tcpping >/dev/null 2>&1; then
    echo "a) 最简单的测试:"
    tcpping $HOST -p $PORT -c 1 2>&1
    echo
    
    echo "b) 不使用 -t 参数:"
    tcpping $HOST -p $PORT -c 1 2>&1
    echo
    
    echo "c) 检查是否支持 -i 参数:"
    tcpping $HOST -p $PORT -c 2 -i 0.5 2>&1
    echo
else
    echo "跳过（tcpping 不存在）"
fi

echo "=== 调试完成 ==="
echo
echo "常见问题和解决方案："
echo "1. 如果 tcpping 不存在: sudo yum install tcptraceroute"
echo "2. 如果脚本不存在: sudo cp scripts/check_tcping_single.sh /etc/zabbix/scripts/"
echo "3. 如果权限问题: sudo chown zabbix:zabbix /etc/zabbix/scripts/check_tcping_single.sh"
echo "4. 如果端口不通: 检查目标服务是否运行"
echo "5. 如果参数不支持: 可能需要调整 tcpping 参数"
