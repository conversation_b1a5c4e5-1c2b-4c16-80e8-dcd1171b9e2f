# Zabbix TCP ping 监控配置
# 将此配置添加到 /etc/zabbix/zabbix_agentd.d/tcping.conf

# 基础 TCP ping 检查
UserParameter=check.tcping[*],/etc/zabbix/scripts/check_tcping.sh $1 $2 $3

# 增强版 TCP ping 检查（包含超时控制）
UserParameter=check.tcping.enhanced[*],/etc/zabbix/scripts/check_tcping_enhanced.sh $1 $2 $3 $4

# 预定义的常用服务检查
UserParameter=check.tcping.http[*],/etc/zabbix/scripts/check_tcping.sh $1 80 ${2:-3}
UserParameter=check.tcping.https[*],/etc/zabbix/scripts/check_tcping.sh $1 443 ${2:-3}
UserParameter=check.tcping.ssh[*],/etc/zabbix/scripts/check_tcping.sh $1 22 ${2:-3}
UserParameter=check.tcping.ftp[*],/etc/zabbix/scripts/check_tcping.sh $1 21 ${2:-3}
UserParameter=check.tcping.smtp[*],/etc/zabbix/scripts/check_tcping.sh $1 25 ${2:-3}
UserParameter=check.tcping.dns[*],/etc/zabbix/scripts/check_tcping.sh $1 53 ${2:-3}
UserParameter=check.tcping.mysql[*],/etc/zabbix/scripts/check_tcping.sh $1 3306 ${2:-3}
UserParameter=check.tcping.redis[*],/etc/zabbix/scripts/check_tcping.sh $1 6379 ${2:-3}
