#!/bin/bash

# Zabbix TCP ping 监控脚本部署工具
# 用法: ./deploy.sh [选项]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为 root 用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 root 权限运行此脚本"
        echo "使用方法: sudo $0"
        exit 1
    fi
}

# 检查 tcpping 命令
check_tcpping() {
    log_info "检查 tcpping 命令..."
    if ! command -v tcpping >/dev/null 2>&1; then
        log_warning "tcpping 命令未找到，尝试安装..."
        
        # 检测系统类型并安装
        if command -v yum >/dev/null 2>&1; then
            log_info "检测到 CentOS/RHEL 系统，使用 yum 安装..."
            yum install -y tcptraceroute || yum install -y tcpping
        elif command -v apt-get >/dev/null 2>&1; then
            log_info "检测到 Ubuntu/Debian 系统，使用 apt 安装..."
            apt-get update && apt-get install -y tcptraceroute
        else
            log_error "无法自动安装 tcpping，请手动安装"
            exit 1
        fi
        
        # 再次检查
        if ! command -v tcpping >/dev/null 2>&1; then
            log_error "tcpping 安装失败"
            exit 1
        fi
    fi
    log_success "tcpping 命令检查通过"
}

# 检查 Zabbix Agent
check_zabbix_agent() {
    log_info "检查 Zabbix Agent..."
    
    if ! command -v zabbix_agentd >/dev/null 2>&1; then
        log_error "Zabbix Agent 未安装，请先安装 Zabbix Agent"
        exit 1
    fi
    
    # 检查 zabbix 用户
    if ! id zabbix >/dev/null 2>&1; then
        log_error "zabbix 用户不存在"
        exit 1
    fi
    
    log_success "Zabbix Agent 检查通过"
}

# 创建目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p /etc/zabbix/scripts
    mkdir -p /etc/zabbix/zabbix_agentd.d
    
    log_success "目录创建完成"
}

# 部署脚本文件
deploy_scripts() {
    log_info "部署监控脚本..."
    
    # 获取当前脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制脚本文件
    cp "$SCRIPT_DIR/scripts/check_tcping.sh" /etc/zabbix/scripts/
    cp "$SCRIPT_DIR/scripts/check_tcping_enhanced.sh" /etc/zabbix/scripts/
    
    # 设置权限
    chmod +x /etc/zabbix/scripts/check_tcping.sh
    chmod +x /etc/zabbix/scripts/check_tcping_enhanced.sh
    
    # 设置所有者
    chown zabbix:zabbix /etc/zabbix/scripts/check_tcping*.sh
    
    log_success "脚本文件部署完成"
}

# 部署配置文件
deploy_config() {
    log_info "部署 Zabbix 配置..."
    
    # 获取当前脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制配置文件
    cp "$SCRIPT_DIR/config/tcping.conf" /etc/zabbix/zabbix_agentd.d/
    
    # 设置权限
    chown zabbix:zabbix /etc/zabbix/zabbix_agentd.d/tcping.conf
    chmod 644 /etc/zabbix/zabbix_agentd.d/tcping.conf
    
    log_success "配置文件部署完成"
}

# 测试脚本
test_scripts() {
    log_info "测试脚本功能..."
    
    # 测试基础版本
    log_info "测试基础版本..."
    if sudo -u zabbix /etc/zabbix/scripts/check_tcping.sh ******* 53 3 >/dev/null 2>&1; then
        log_success "基础版本测试通过"
    else
        log_warning "基础版本测试失败，请检查网络连接"
    fi
    
    # 测试增强版本
    log_info "测试增强版本..."
    if sudo -u zabbix /etc/zabbix/scripts/check_tcping_enhanced.sh ******* 53 3 5 >/dev/null 2>&1; then
        log_success "增强版本测试通过"
    else
        log_warning "增强版本测试失败，请检查网络连接"
    fi
}

# 重启 Zabbix Agent
restart_zabbix_agent() {
    log_info "重启 Zabbix Agent..."
    
    if systemctl is-active --quiet zabbix-agent; then
        systemctl restart zabbix-agent
        log_success "Zabbix Agent 重启完成"
    elif systemctl is-active --quiet zabbix-agentd; then
        systemctl restart zabbix-agentd
        log_success "Zabbix Agent 重启完成"
    else
        log_warning "无法确定 Zabbix Agent 服务名称，请手动重启"
    fi
}

# 验证部署
verify_deployment() {
    log_info "验证部署结果..."
    
    # 测试 Zabbix Agent 配置
    log_info "测试 Zabbix Agent 配置..."
    if zabbix_agentd -t check.tcping[*******,53,3] >/dev/null 2>&1; then
        log_success "Zabbix Agent 配置测试通过"
    else
        log_warning "Zabbix Agent 配置测试失败"
    fi
}

# 显示使用说明
show_usage() {
    log_info "部署完成！"
    echo
    echo "使用方法："
    echo "1. 测试基础版本："
    echo "   zabbix_agentd -t check.tcping[*******,53,3]"
    echo
    echo "2. 测试增强版本："
    echo "   zabbix_agentd -t check.tcping.enhanced[*******,53,3,5]"
    echo
    echo "3. 测试预定义服务："
    echo "   zabbix_agentd -t check.tcping.https[www.google.com,3]"
    echo
    echo "4. 调试模式（仅增强版本）："
    echo "   DEBUG=1 /etc/zabbix/scripts/check_tcping_enhanced.sh ******* 53 3"
    echo
}

# 主函数
main() {
    log_info "开始部署 Zabbix TCP ping 监控脚本..."
    
    check_root
    check_tcpping
    check_zabbix_agent
    create_directories
    deploy_scripts
    deploy_config
    test_scripts
    restart_zabbix_agent
    verify_deployment
    show_usage
    
    log_success "部署完成！"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        echo "Zabbix TCP ping 监控脚本部署工具"
        echo "用法: $0 [选项]"
        echo "选项:"
        echo "  -h, --help    显示此帮助信息"
        echo "  --test-only   仅运行测试，不进行部署"
        exit 0
        ;;
    --test-only)
        log_info "仅运行测试模式..."
        test_scripts
        verify_deployment
        exit 0
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        echo "使用 $0 --help 查看帮助"
        exit 1
        ;;
esac
