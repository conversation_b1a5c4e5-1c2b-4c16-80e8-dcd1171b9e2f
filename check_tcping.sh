#!/bin/bash

# Zabbix TCP ping 监控脚本
# 用法: check_tcping.sh <HOST> <PORT> [COUNT]
# 返回: 平均延迟(ms) 或 -1(失败)

HOST=$1
PORT=$2
COUNT=${3:-3}

# 参数检查
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    echo -1
    exit 1
fi

# 检查 COUNT 是否为正整数
if ! [[ "$COUNT" =~ ^[1-9][0-9]*$ ]]; then
    COUNT=3
fi

# 执行 tcpping 命令并解析结果
# 注意：根据您的测试，tcpping 使用 -c 参数，但如果您的版本使用 -x，请相应修改
RESULT=$(tcpping $HOST -p $PORT -c $COUNT 2>/dev/null | \
    grep 'time=' | awk -F'time=' '{print $2}' | awk '{gsub(/[^0-9.]/, "", $1); sum+=$1; count++} END {if(count>0) printf "%.3f", sum/count; else print -1}')

# 检查结果是否为空或无效
if [ -z "$RESULT" ] || [ "$RESULT" = "-1" ]; then
    echo -1
else
    echo $RESULT
fi
