#!/bin/bash

# Zabbix TCP ping 监控脚本 (调试版本)
# 用法: check_tcping_debug.sh <HOST> <PORT>
# 返回: 连接延迟(ms) 或 -1(失败)
# 特点: 输出详细调试信息到 stderr

HOST=$1
PORT=$2

# 调试输出函数
debug_log() {
    if [ "$DEBUG" = "1" ]; then
        echo "[DEBUG] $1" >&2
    fi
}

debug_log "开始执行 tcping 测试: $HOST:$PORT"

# 参数检查
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    debug_log "参数错误: HOST=$HOST, PORT=$PORT"
    echo -1
    exit 1
fi

# 检查 tcpping 命令是否存在
if ! command -v tcpping >/dev/null 2>&1; then
    debug_log "tcpping 命令不存在"
    echo -1
    exit 1
fi

debug_log "tcpping 命令路径: $(which tcpping)"

# 测试端口连通性
debug_log "测试端口连通性..."
if command -v nc >/dev/null 2>&1; then
    if ! timeout 2 nc -z $HOST $PORT >/dev/null 2>&1; then
        debug_log "端口 $HOST:$PORT 不可达"
        echo -1
        exit 1
    fi
    debug_log "端口连通性检查通过"
fi

# 执行 tcpping 命令
debug_log "执行 tcpping 命令..."

# 尝试不同的命令格式
COMMANDS=(
    "tcpping $HOST -p $PORT -c 1"
    "tcpping $HOST -p $PORT -c 1 -t 2"
    "tcpping $HOST -p $PORT -x 1"
    "tcpping $HOST $PORT"
)

for cmd in "${COMMANDS[@]}"; do
    debug_log "尝试命令: $cmd"
    
    # 执行命令
    TCPPING_OUTPUT=$(timeout 5 $cmd 2>&1)
    EXIT_CODE=$?
    
    debug_log "命令退出码: $EXIT_CODE"
    debug_log "命令输出: $TCPPING_OUTPUT"
    
    if [ $EXIT_CODE -eq 0 ] && [ -n "$TCPPING_OUTPUT" ]; then
        # 尝试解析结果
        RESULT=$(echo "$TCPPING_OUTPUT" | grep 'time=' | head -1)
        debug_log "匹配到的行: $RESULT"
        
        if [ -n "$RESULT" ]; then
            # 提取时间值
            TIME_VALUE=$(echo "$RESULT" | awk -F'time=' '{print $2}' | awk '{gsub(/[^0-9.]/, "", $1); print $1}')
            debug_log "提取的时间值: $TIME_VALUE"
            
            if [ -n "$TIME_VALUE" ] && [[ "$TIME_VALUE" =~ ^[0-9]+\.?[0-9]*$ ]] && [ "$(echo "$TIME_VALUE > 0" | bc 2>/dev/null || echo 0)" = "1" ]; then
                debug_log "成功解析时间: $TIME_VALUE"
                printf "%.3f" "$TIME_VALUE"
                exit 0
            fi
        fi
    fi
done

debug_log "所有命令都失败了"
echo -1
exit 1
