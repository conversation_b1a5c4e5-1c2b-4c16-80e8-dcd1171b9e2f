#!/bin/bash

# Zabbix TCP ping 监控脚本 (单次测试版本)
# 用法: check_tcping_single.sh <HOST> <PORT>
# 返回: 连接延迟(ms) 或 -1(失败)
# 特点: 最快速度，适合大规模监控

HOST=$1
PORT=$2

# 设置超时时间（秒）
TIMEOUT=3

# 参数检查
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    echo -1
    exit 1
fi

# 检查 tcpping 命令是否存在
if ! command -v tcpping >/dev/null 2>&1; then
    echo -1
    exit 1
fi

# 执行单次 tcpping 测试
# -c 1: 只测试1次
# -t 2: 单包超时2秒
TCPPING_OUTPUT=$(timeout $TIMEOUT tcpping $HOST -p $PORT -c 1 -t 2 2>/dev/null)
EXIT_CODE=$?

# 检查命令是否成功执行
if [ $EXIT_CODE -ne 0 ]; then
    echo -1
    exit 1
fi

# 解析结果
RESULT=$(echo "$TCPPING_OUTPUT" | \
    grep 'time=' | \
    awk -F'time=' '{
        gsub(/[^0-9.]/, "", $2)
        if ($2 > 0) {
            printf "%.3f", $2
        } else {
            print -1
        }
    }')

# 检查结果是否为空或无效
if [ -z "$RESULT" ] || [ "$RESULT" = "-1" ]; then
    echo -1
else
    # 验证结果是数字
    if [[ "$RESULT" =~ ^[0-9]+\.?[0-9]*$ ]]; then
        echo $RESULT
    else
        echo -1
    fi
fi
