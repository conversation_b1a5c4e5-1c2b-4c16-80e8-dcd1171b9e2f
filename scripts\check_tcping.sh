#!/bin/bash

# Zabbix TCP ping 监控脚本
# 用法: check_tcping.sh <HOST> <PORT>
# 返回: 平均延迟(ms) 或 -1(失败)

HOST=$1
PORT=$2

# 参数检查
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    echo -1
    exit 1
fi

# 执行 tcpping 命令
RESULT=$(tcpping $HOST -p $PORT -c 3 2>/dev/null | \
    grep 'time=' | awk -F'time=' '{print $2}' | \
    awk '{gsub(/[^0-9.]/, "", $1); sum+=$1; count++} END {if(count>0) printf "%.3f", sum/count; else print -1}')

# 输出结果
if [ -z "$RESULT" ] || [ "$RESULT" = "-1" ]; then
    echo -1
else
    echo $RESULT
fi
