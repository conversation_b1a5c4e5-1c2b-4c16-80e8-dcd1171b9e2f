#!/bin/bash

# Zabbix TCP ping 监控脚本 (优化版)
# 用法: check_tcping.sh <HOST> <PORT> [COUNT]
# 返回: 平均延迟(ms) 或 -1(失败)

HOST=$1
PORT=$2
COUNT=${3:-3}

# 设置超时时间（秒）
TIMEOUT=8

# 参数检查
if [ -z "$HOST" ] || [ -z "$PORT" ]; then
    echo -1
    exit 1
fi

# 检查 COUNT 是否为正整数，限制最大值
if ! [[ "$COUNT" =~ ^[1-9][0-9]*$ ]] || [ "$COUNT" -gt 10 ]; then
    COUNT=3
fi

# 检查 tcpping 命令是否存在
if ! command -v tcpping >/dev/null 2>&1; then
    echo -1
    exit 1
fi

# 执行 tcpping 命令并解析结果，添加超时控制和间隔优化
# -i 0.2 设置包间隔为0.2秒，大大减少总执行时间
# -t 2 设置单个包超时为2秒
TCPPING_OUTPUT=$(timeout $TIMEOUT tcpping $HOST -p $PORT -c $COUNT -i 0.2 -t 2 2>/dev/null)
EXIT_CODE=$?

# 检查命令是否成功执行
if [ $EXIT_CODE -ne 0 ]; then
    echo -1
    exit 1
fi

# 解析结果并计算平均延迟
RESULT=$(echo "$TCPPING_OUTPUT" | \
    grep 'time=' | \
    awk -F'time=' '{
        gsub(/[^0-9.]/, "", $2)
        if ($2 > 0) {
            sum += $2
            count++
        }
    } END {
        if (count > 0) {
            printf "%.3f", sum/count
        } else {
            print -1
        }
    }')

# 检查结果是否为空或无效
if [ -z "$RESULT" ] || [ "$RESULT" = "-1" ]; then
    echo -1
else
    # 验证结果是数字
    if [[ "$RESULT" =~ ^[0-9]+\.?[0-9]*$ ]]; then
        echo $RESULT
    else
        echo -1
    fi
fi
