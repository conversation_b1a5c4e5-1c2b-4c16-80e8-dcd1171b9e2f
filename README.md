# Zabbix TCP Ping 监控脚本

## 概述

简单的 Zabbix TCP ping 监控脚本，用于检查 TCP 端口连通性和响应时间。

## 文件结构

```
.
├── scripts/
│   └── check_tcping.sh        # TCP ping 检查脚本
├── config/
│   └── tcping.conf            # Zabbix 配置文件
└── README.md                   # 使用说明
```

## 安装步骤

### 1. 复制脚本文件
```bash
sudo mkdir -p /etc/zabbix/scripts
sudo cp scripts/check_tcping.sh /etc/zabbix/scripts/
sudo chmod +x /etc/zabbix/scripts/check_tcping.sh
sudo chown zabbix:zabbix /etc/zabbix/scripts/check_tcping.sh
```

### 2. 配置 Zabbix Agent
```bash
sudo cp config/tcping.conf /etc/zabbix/zabbix_agentd.d/
sudo systemctl restart zabbix-agent
```

### 3. 测试
```bash
zabbix_agentd -t tcping.port[127.0.0.1,80]
zabbix_get -s 客户端IP -k tcping.port[127.0.0.1,80]
```

## 使用方法

### Zabbix 前端配置
- **监控项键值**: `tcping.port[127.0.0.1,80]`
- **数据类型**: 数值（浮点）
- **单位**: ms

### 命令行测试
```bash
# 手动测试脚本
/etc/zabbix/scripts/check_tcping.sh 127.0.0.1 80

# 测试 zabbix_agentd
zabbix_agentd -t tcping.port[127.0.0.1,80]

# 测试 zabbix_get
zabbix_get -s 客户端IP -k tcping.port[127.0.0.1,80]
```

## 返回值
- **成功**: 返回平均响应时间（毫秒，3位小数）
- **失败**: 返回 -1

## 故障排除
1. 确保 tcpping 已安装: `sudo yum install tcptraceroute`
2. 检查脚本权限: `ls -la /etc/zabbix/scripts/check_tcping.sh`
3. 手动测试: `sudo -u zabbix /etc/zabbix/scripts/check_tcping.sh 127.0.0.1 80`
