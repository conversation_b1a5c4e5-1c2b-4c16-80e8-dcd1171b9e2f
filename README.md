# Zabbix TCP Ping 监控脚本

## 概述

这个项目提供了用于 Zabbix 监控系统的 TCP ping 检查脚本，可以监控目标主机的 TCP 端口连通性和响应时间。

## 目录结构

```
.
├── scripts/                    # 监控脚本目录
│   ├── check_tcping.sh        # 基础版本的 TCP ping 检查脚本
│   └── check_tcping_enhanced.sh # 增强版本，包含更完善的错误处理和日志功能
├── config/                     # 配置文件目录
│   └── tcping.conf            # Zabbix 配置文件
├── deploy.sh                   # 一键部署脚本
└── README.md                   # 使用说明文档
```

## 快速安装

### 方法一：一键部署（推荐）

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 运行部署脚本（需要 root 权限）
sudo ./deploy.sh
```

部署脚本会自动：
- 检查并安装 tcpping 工具
- 检查 Zabbix Agent 环境
- 创建必要目录
- 复制脚本和配置文件
- 设置正确的权限
- 重启 Zabbix Agent
- 运行测试验证

### 方法二：手动安装

#### 1. 安装脚本

```bash
# 创建脚本目录
sudo mkdir -p /etc/zabbix/scripts

# 复制脚本文件
sudo cp scripts/check_tcping.sh /etc/zabbix/scripts/
sudo cp scripts/check_tcping_enhanced.sh /etc/zabbix/scripts/

# 设置执行权限
sudo chmod +x /etc/zabbix/scripts/check_tcping.sh
sudo chmod +x /etc/zabbix/scripts/check_tcping_enhanced.sh

# 设置所有者
sudo chown zabbix:zabbix /etc/zabbix/scripts/check_tcping*.sh
```

#### 2. 配置 Zabbix Agent

```bash
# 复制配置文件
sudo cp config/tcping.conf /etc/zabbix/zabbix_agentd.d/

# 重启 Zabbix Agent
sudo systemctl restart zabbix-agent
```

#### 3. 验证安装

```bash
# 测试基础版本
zabbix_agentd -t check.tcping[*******,53,3]

# 测试增强版本
zabbix_agentd -t check.tcping.enhanced[*******,53,3,5]

# 测试预定义服务
zabbix_agentd -t check.tcping.https[www.google.com,3]
```

## 使用方法

### 基础版本

```bash
# 语法
check.tcping[HOST,PORT,COUNT]

# 参数说明
# HOST  - 目标主机 IP 或域名
# PORT  - 目标端口号
# COUNT - 测试次数（可选，默认 3）

# 示例
check.tcping[***********,80,5]
check.tcping[www.example.com,443]
```

### 增强版本

```bash
# 语法
check.tcping.enhanced[HOST,PORT,COUNT,TIMEOUT]

# 参数说明
# HOST    - 目标主机 IP 或域名
# PORT    - 目标端口号
# COUNT   - 测试次数（可选，默认 3）
# TIMEOUT - 超时时间秒数（可选，默认 5）

# 示例
check.tcping.enhanced[***********,80,5,10]
check.tcping.enhanced[www.example.com,443,3]
```

### 预定义服务检查

```bash
# HTTP 服务
check.tcping.http[www.example.com,5]

# HTTPS 服务
check.tcping.https[www.example.com,3]

# SSH 服务
check.tcping.ssh[***********]

# DNS 服务
check.tcping.dns[*******,5]

# MySQL 服务
check.tcping.mysql[db.example.com]
```

## 返回值

- **成功**: 返回平均响应时间（毫秒，保留3位小数）
- **失败**: 返回 -1

## 故障排除

### 1. 权限问题

```bash
# 确保脚本有执行权限
ls -la /etc/zabbix/scripts/check_tcping*.sh

# 确保 zabbix 用户可以执行
sudo -u zabbix /etc/zabbix/scripts/check_tcping.sh ******* 53 3
```

### 2. tcpping 命令不存在

```bash
# 检查 tcpping 是否安装
which tcpping

# CentOS/RHEL 安装 tcpping
sudo yum install tcptraceroute
# 或者
sudo yum install tcpping

# Ubuntu/Debian 安装
sudo apt-get install tcptraceroute
```

### 3. 调试模式

```bash
# 启用调试模式（仅增强版本）
DEBUG=1 /etc/zabbix/scripts/check_tcping_enhanced.sh ******* 53 3
```

### 4. 检查 Zabbix Agent 日志

```bash
# 查看 Zabbix Agent 日志
sudo tail -f /var/log/zabbix/zabbix_agentd.log
```

## 注意事项

1. **tcpping 参数**: 根据您的 tcpping 版本，可能需要将脚本中的 `-c` 参数改为 `-x`
2. **防火墙**: 确保目标端口没有被防火墙阻止
3. **网络延迟**: 对于高延迟网络，建议增加超时时间
4. **资源使用**: 避免同时进行大量的 TCP ping 检查，以免影响系统性能

## 许可证

本项目采用 MIT 许可证。
